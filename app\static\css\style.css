/* Modern Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Enhanced Dark Theme Variables - Custom Color Palette */
:root {
    /* Dark theme (default) - using custom color palette */
    --primary: #4a1a4a; /* Deep purple from palette */
    --primary-dark: #3d1a3d; /* Darker purple */
    --secondary: #ff6b47; /* Orange accent from palette */
    --accent: #ff6b47; /* Orange from palette */
    --accent-purple: #4a1a4a; /* Deep purple */
    --accent-teal: #6b5b4a; /* Olive/brown from palette */
    --accent-pink: #ff6b47; /* Orange */
    --accent-blue: #4a1a4a; /* Deep purple */
    --accent-yellow: #f5f5dc; /* Cream from palette */
    --accent-orange: #ff6b47; /* Orange from palette */
    --light: #f5f5dc; /* Cream from palette */
    --dark: #4a1a4a; /* Deep purple background */
    --dark-accent: #6b5b4a; /* Olive/brown accent */
    --dark-surface: #5a4a5a; /* Slightly lighter purple surface */
    --text-color: var(--light);
    --bg-color: var(--dark);
    --card-bg: rgba(107, 91, 74, 0.3); /* Olive/brown with transparency */
    --input-bg: rgba(90, 74, 90, 0.5); /* Purple with transparency */
    --border-color: rgba(245, 245, 220, 0.15); /* Cream borders */
    --shadow: 0 4px 12px rgba(74, 26, 74, 0.4);
    --transition: all 0.3s ease;

    /* Enhanced Gradients using custom palette */
    --header-gradient: linear-gradient(135deg, #4a1a4a, #6b5b4a, #5a4a5a);
    --footer-gradient: linear-gradient(135deg, #4a1a4a, #6b5b4a, #5a4a5a);
    --button-gradient: linear-gradient(135deg, #ff6b47, #4a1a4a, #6b5b4a);
    --body-gradient: linear-gradient(-45deg, #4a1a4a, #6b5b4a, #5a4a5a, #4a2a4a, #6a5a6a, #4a1a4a);
    --card-gradient: linear-gradient(135deg, rgba(107, 91, 74, 0.3), rgba(90, 74, 90, 0.3));
    --accent-gradient: linear-gradient(135deg, #ff6b47, #4a1a4a);
}

/* Enhanced body background with animated gradient */
body {
    font-family: 'Inter', sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: var(--dark);
    position: relative;
    min-height: 100vh;
    overflow-x: hidden;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--body-gradient);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    opacity: 0.8;
    z-index: -1;
}

/* Add subtle animated particles to the background */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.particle {
    position: absolute;
    border-radius: 50%;
    opacity: 0.5;
    box-shadow: 0 0 10px 2px currentColor;
    animation: float 15s infinite ease-in-out;
}

/* Create different colored particles using custom palette */
.particle:nth-child(5n+1) { color: #4a1a4a; } /* Deep purple */
.particle:nth-child(5n+2) { color: #6b5b4a; } /* Olive/brown */
.particle:nth-child(5n+3) { color: #ff6b47; } /* Orange */
.particle:nth-child(5n+4) { color: #f5f5dc; } /* Cream */
.particle:nth-child(5n+5) { color: #5a4a5a; } /* Light purple */

/* Enhanced card styling with subtle glow */
.entry-card {
    background: var(--card-gradient);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
}

.entry-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--accent-gradient);
    z-index: -1;
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.entry-card:hover::before {
    opacity: 0.5;
}

.entry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.3);
}

/* Enhanced button styling with animated gradient */
button, .button {
    background: var(--button-gradient);
    background-size: 200% 200%;
    animation: gradientShift 5s ease infinite;
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-block;
    text-decoration: none;
    text-align: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
    box-shadow: 0 4px 15px rgba(255, 107, 71, 0.4);
}

button:hover, .button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 71, 0.6);
}

/* Enhanced form input styling */
input, textarea, select {
    background: var(--input-bg);
    border: 1px solid var(--border-color);
    color: var(--text-color);
    padding: 0.8rem 1rem;
    border-radius: 6px;
    width: 100%;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

input:focus, textarea:focus, select:focus {
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 71, 0.3);
    outline: none;
}

/* Add colorful accents to various UI elements using custom palette */
.tag {
    background: rgba(107, 91, 74, 0.3);
    color: var(--accent-orange);
    border: 1px solid rgba(255, 107, 71, 0.4);
    border-radius: 20px;
    padding: 0.3rem 0.8rem;
    font-size: 0.8rem;
    display: inline-block;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    transition: var(--transition);
}

.tag:hover {
    background: var(--accent-orange);
    color: var(--light);
    transform: translateY(-2px);
}

/* Add gradient animations */
@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Add floating animation for particles */
@keyframes float {
    0% { transform: translateY(0) translateX(0); }
    25% { transform: translateY(-20px) translateX(10px); }
    50% { transform: translateY(0) translateX(20px); }
    75% { transform: translateY(20px) translateX(10px); }
    100% { transform: translateY(0) translateX(0); }
}

/* Enhanced header with more vibrant gradient */
header {
    background: var(--header-gradient);
    background-size: 200% 200%;
    animation: gradientShift 10s ease infinite;
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--border-color);
}

/* Enhanced footer with more vibrant gradient */
footer {
    background: var(--footer-gradient);
    background-size: 200% 200%;
    animation: gradientShift 10s ease infinite;
    color: white;
    padding: 2rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-top: 1px solid var(--border-color);
}

/* Light theme variables with much darker text for maximum contrast */
[data-theme="light"] {
    --primary: #4a26fd;
    --primary-dark: #3b1de0;
    --secondary: #e74c3c;
    --accent: #16a085;
    --accent-purple: #8e44ad;
    --accent-teal: #00bcd4;
    --accent-pink: #e91e63;
    --accent-blue: #1e88e5;
    --light: #ffffff;
    --dark: #121212;
    --dark-accent: #1e1e24;
    --dark-surface: #252525;
    --text-color: #000000; /* Pure black text for maximum contrast */
    --text-secondary: #222222; /* Very dark secondary text */
    --text-muted: #444444; /* Darker muted text */
    --bg-color: #f0f4f8;
    --card-bg: #ffffff;
    --input-bg: #ffffff;
    --border-color: rgba(0, 0, 0, 0.12);
    --shadow: 0 4px 12px rgba(0,0,0,0.1);

    /* Light theme gradients */
    --header-gradient: linear-gradient(135deg, #6a3093, #4a26fd);
    --footer-gradient: linear-gradient(135deg, #6a3093, #4a26fd);
    --button-gradient: linear-gradient(135deg, #8e44ad, #4a26fd);
    --body-gradient: linear-gradient(-45deg, #f0f4f8, #e6f7ff, #f5f0ff, #fff0f6);
}

/* Light theme specific overrides - more vibrant and attractive */
[data-theme="light"] body {
    background-color: #f0f4f8;
    background-image:
        radial-gradient(circle at 10% 10%, rgba(74, 38, 253, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 90% 20%, rgba(231, 76, 60, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 50% 50%, rgba(22, 160, 133, 0.03) 0%, transparent 40%),
        radial-gradient(circle at 80% 80%, rgba(142, 68, 173, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 20% 70%, rgba(0, 188, 212, 0.05) 0%, transparent 30%),
        radial-gradient(circle at 70% 10%, rgba(233, 30, 99, 0.05) 0%, transparent 30%);
}

[data-theme="light"] body::before {
    background: linear-gradient(
        -45deg,
        #f0f4f8,
        #e6f7ff,
        #f5f0ff,
        #fff0f6,
        #f0f8ff,
        #f8f0ff
    );
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
    opacity: 0.8;
}

[data-theme="light"] .container {
    background-color: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    margin-top: 20px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .entry-card {
    background-color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

[data-theme="light"] .entry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    border-color: rgba(74, 38, 253, 0.2);
}

[data-theme="light"] .entry-card h3 {
    color: #000000;
    font-weight: 700;
}

[data-theme="light"] .entry-card .entry-date {
    color: var(--text-secondary);
    font-weight: 600;
}

[data-theme="light"] .entry-card .entry-preview {
    color: var(--text-color);
    font-weight: 500;
}

[data-theme="light"] .journal-detail {
    background-color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid rgba(0, 0, 0, 0.05);
    padding: 25px;
    border-radius: 12px;
}

[data-theme="light"] .journal-detail h2 {
    color: #000000;
    font-weight: 700;
}

[data-theme="light"] .journal-detail .entry-date {
    color: var(--text-secondary);
    font-weight: 600;
}

[data-theme="light"] .journal-detail .entry-content {
    color: var(--text-color);
    line-height: 1.7;
    font-weight: 500;
}

[data-theme="light"] header {
    background: var(--header-gradient);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

[data-theme="light"] header h1 {
    color: white;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] nav a {
    color: rgba(255, 255, 255, 0.95);
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] nav a:hover {
    color: white;
}

[data-theme="light"] footer {
    background: var(--footer-gradient);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .auth-container {
    background-color: #ffffff;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    padding: 30px;
}

[data-theme="light"] .auth-container h2 {
    color: #000000;
    font-weight: 700;
}

[data-theme="light"] .auth-container p {
    color: var(--text-color);
    font-weight: 500;
}

[data-theme="light"] input,
[data-theme="light"] textarea,
[data-theme="light"] select {
    background-color: #ffffff;
    border: 1px solid rgba(0, 0, 0, 0.15);
    color: #000000;
    font-weight: 500;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
    transition: all 0.3s ease;
}

[data-theme="light"] input:focus,
[data-theme="light"] textarea:focus,
[data-theme="light"] select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(74, 38, 253, 0.15);
    outline: none;
}

[data-theme="light"] input:hover,
[data-theme="light"] textarea:hover,
[data-theme="light"] select:hover {
    border-color: rgba(0, 0, 0, 0.3);
    background-color: #fafafa;
}

/* Light theme buttons - more vibrant */
[data-theme="light"] button,
[data-theme="light"] .button {
    background: var(--button-gradient);
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    box-shadow: 0 4px 10px rgba(74, 38, 253, 0.2);
    transition: all 0.3s ease;
}

[data-theme="light"] button:hover,
[data-theme="light"] .button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(74, 38, 253, 0.3);
}

[data-theme="light"] button:active,
[data-theme="light"] .button:active {
    transform: translateY(-1px);
}

/* Light theme secondary buttons */
[data-theme="light"] .button.secondary {
    background: #f0f0f0;
    color: #222222;
    font-weight: 600;
    border: 1px solid rgba(0, 0, 0, 0.1);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .button.secondary:hover {
    background: #e6e6e6;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.08);
}

/* Light theme danger buttons */
[data-theme="light"] .button.danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    box-shadow: 0 4px 10px rgba(231, 76, 60, 0.2);
}

[data-theme="light"] .button.danger:hover {
    box-shadow: 0 6px 15px rgba(231, 76, 60, 0.3);
}

/* Light theme form */
[data-theme="light"] form {
    background-color: #ffffff;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    border-radius: 12px;
    border: 1px solid rgba(0, 0, 0, 0.05);
    padding: 25px;
}

[data-theme="light"] form label {
    color: #000000;
    font-weight: 700;
}

/* Light theme placeholder */
[data-theme="light"] ::placeholder {
    color: #333333;
    opacity: 0.8;
}

/* Light theme links */
[data-theme="light"] a {
    color: #2a148c; /* Very dark link color */
    font-weight: 600;
    transition: all 0.3s ease;
}

[data-theme="light"] a:hover {
    color: #1a0a5c; /* Almost black on hover */
    text-decoration: underline;
}

/* Light theme particles */
[data-theme="light"] .particle {
    opacity: 0.3;
    box-shadow: 0 0 8px 1px currentColor;
}

/* Container */
.container {
    width: 90%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Enhanced Header with Vibrant Gradient */
header {
    background: var(--header-gradient);
    color: white;
    padding: 1.5rem 0;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    position: sticky;
    top: 0;
    z-index: 100;
    border-bottom: 1px solid var(--border-color);
}

/* Add a subtle pattern overlay to the header */
header::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%236d5dfc' fill-opacity='0.05' fill-rule='evenodd'%3E%3Cpath d='M0 40L40 0H20L0 20M40 40V20L20 40'/%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo img {
    height: 40px;
    transition: transform 0.5s ease;
    margin-right: 12px;
}

.logo:hover img {
    transform: rotate(10deg) scale(1.1);
}

header h1 {
    margin: 0;
    font-family: 'Playfair Display', serif;
    font-size: 1.8rem;
    font-weight: 700;
    letter-spacing: 0.5px;
}

nav {
    display: flex;
    gap: 1.5rem;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 4px;
    transition: var(--transition);
}

nav a:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

/* Main Content */
main {
    padding: 2.5rem 0;
    min-height: calc(100vh - 160px);
}

.page-title {
    font-family: 'Playfair Display', serif;
    font-size: 2.2rem;
    margin-bottom: 1.5rem;
    color: var(--light);
    text-align: center;
    position: relative;
}

.page-title::after {
    content: '';
    display: block;
    width: 80px;
    height: 4px;
    background: var(--accent-orange);
    margin: 12px auto;
    border-radius: 2px;
}

/* Forms */
/* Enhanced Form Styles with Frosted Glass Effect */
form {
    background: var(--card-bg);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: var(--shadow);
    border-top: 5px solid var(--accent-orange);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

form:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0,0,0,0.2);
}

.form-group {
    margin-bottom: 1.5rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-color);
}

/* Make form placeholders brighter and more visible */
::placeholder {
    color: var(--text-color);
    opacity: 0.7;
}

:-ms-input-placeholder {
    color: var(--text-color);
}

::-ms-input-placeholder {
    color: var(--text-color);
}

/* Enhanced form input styles with better visibility */
input, textarea, select {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: 6px;
    font-family: inherit;
    font-size: 1rem;
    transition: var(--transition);
    background-color: var(--input-bg);
    color: var(--text-color);
}

input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--accent-orange);
    box-shadow: 0 0 0 3px rgba(255, 107, 71, 0.3);
    background-color: rgba(90, 74, 90, 0.8);
}

/* Add subtle glow to inputs on hover */
input:hover, textarea:hover, select:hover {
    border-color: rgba(245, 245, 220, 0.4);
    background-color: rgba(107, 91, 74, 0.4);
}

/* Enhanced Button Styles with Vibrant Gradient */
button, .button {
    background: var(--button-gradient);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 6px;
    cursor: pointer;
    font-weight: 500;
    transition: var(--transition);
    display: inline-block;
    text-decoration: none;
    text-align: center;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

button::before, .button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.7s ease;
    z-index: -1;
}

button:hover::before, .button:hover::before {
    left: 100%;
}

button:hover, .button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px rgba(0,0,0,0.2);
}

.button.secondary {
    background: var(--gray);
}

.button.secondary:hover {
    background: #5a6268;
}

.button.danger {
    background: var(--danger);
}

.button.danger:hover {
    background: #c82333;
}

/* Journal Entries */
.journal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.entry-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 1.8rem;
}

/* Enhanced Card Styles with Professional Gradient Borders */
.entry-card {
    background: var(--card-bg);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--shadow);
    transition: var(--transition);
    position: relative;
    z-index: 1;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
}

.entry-card::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-vibrant-neon);
    z-index: -1;
    border-radius: 14px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.entry-card:hover::before {
    opacity: 1;
}

.entry-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 20px rgba(0,0,0,0.3);
}

.entry-content {
    padding: 1.5rem;
}

.entry-title {
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
    font-weight: 600;
    color: var(--text-color);
}

.entry-date {
    color: var(--gray);
    font-size: 0.9rem;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 5px;
}

.entry-date i {
    color: var(--secondary);
}

.entry-image {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.entry-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.entry-image:hover img {
    transform: scale(1.05);
}

.entry-tags {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.tag {
    background: #f0f0f0;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    color: var(--gray);
    transition: var(--transition);
}

.tag:hover {
    background: var(--primary);
    color: white;
}

.entry-actions {
    margin-top: 1.2rem;
    display: flex;
    gap: 0.8rem;
}

/* Journal Detail */
.journal-detail {
    background: rgba(22, 33, 62, 0.7);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: var(--shadow);
    max-width: 800px;
    margin: 0 auto;
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.05);
}

.journal-detail .entry-image {
    max-width: 400px;
    max-height: 400px;
    margin: 1.5rem auto;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.journal-detail .entry-image img {
    width: 100%;
    height: auto;
    aspect-ratio: 1/1;
    object-fit: cover;
    cursor: pointer;
}

.journal-detail .entry-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #333;
    padding: 0;
}

.entry-audio {
    margin: 1.5rem 0;
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 8px;
}

.entry-audio audio {
    width: 100%;
}

/* Auth Pages */
.auth-container {
    max-width: 450px;
    margin: 2rem auto;
    background: var(--card-bg);
    padding: 2.5rem;
    border-radius: 12px;
    box-shadow: var(--shadow);
    text-align: center;
    border-top: 5px solid var(--accent-orange);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    color: var(--text-color);
}

.auth-container h2 {
    margin-bottom: 1.5rem;
    color: var(--text-color);
    font-family: 'Playfair Display', serif;
}

.auth-container .form-group {
    text-align: left;
}

.auth-link {
    margin-top: 1.5rem;
    color: var(--gray);
}

.auth-link a {
    color: var(--accent-orange);
    text-decoration: none;
    font-weight: 500;
}

/* Enhanced Footer with Vibrant Gradient */
footer {
    background: var(--footer-gradient);
    color: white;
    padding: 2rem 0;
    text-align: center;
    position: relative;
    overflow: hidden;
    border-top: 1px solid var(--border-color);
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%236d5dfc' fill-opacity='0.05'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
    z-index: -1;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

/* Enhanced Social Links with Horizontal Layout */
.social-links {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin: 1rem 0;
}

.social-links a {
    color: white;
    background: rgba(255,255,255,0.1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.social-links a::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.social-links a:hover::before {
    opacity: 1;
}

.social-links a:hover {
    transform: translateY(-3px) scale(1.1);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

/* Search */
.search-form {
    margin-bottom: 2rem;
    display: flex;
    gap: 1rem;
    background: white;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: var(--shadow);
    align-items: center;
}

.search-form input {
    flex: 1;
    border: none;
    padding: 0.8rem 0;
    font-size: 1rem;
}

.search-form input:focus {
    box-shadow: none;
}

.search-form button {
    padding: 0.8rem 1.2rem;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow);
    grid-column: 1 / -1;
}

.empty-state p {
    margin-bottom: 1.5rem;
    color: var(--gray);
    font-size: 1.1rem;
}

/* Error Messages */
.error-message {
    color: var(--danger);
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

/* Responsive */
@media (max-width: 768px) {
    .entry-list {
        grid-template-columns: 1fr;
    }

    .journal-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    nav {
        flex-wrap: wrap;
    }

    .search-form {
        flex-direction: column;
    }
}

/* Add these new styles for animations and image handling */

/* Logo Animation */
.logo img {
    height: 40px;
    transition: transform 0.5s ease;
}

.logo:hover img {
    transform: rotate(10deg) scale(1.1);
}

/* Animated Icons */
.animated-icon {
    display: inline-block;
    transition: transform 0.3s ease;
}

nav a:hover .animated-icon,
.button:hover .animated-icon {
    transform: translateY(-3px);
}

/* Image Gallery Style */
.entry-image {
    position: relative;
    overflow: hidden;
    cursor: pointer;
}

.entry-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.entry-image:hover img {
    transform: scale(1.05);
}

/* Journal Detail Image */
.journal-detail .entry-image {
    max-width: 400px;
    max-height: 400px;
    margin: 1.5rem auto;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--shadow);
}

.journal-detail .entry-image img {
    width: 100%;
    height: auto;
    aspect-ratio: 1/1;
    object-fit: cover;
    cursor: pointer;
}

/* Image Modal for Expanded View */
.image-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    justify-content: center;
    align-items: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.image-modal.active {
    display: flex;
    opacity: 1;
}

.modal-content {
    max-width: 80%;
    max-height: 80%;
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.close-modal {
    position: absolute;
    top: 20px;
    right: 30px;
    color: white;
    font-size: 30px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.close-modal:hover {
    transform: rotate(90deg);
}

/* Pulse Animation for New Entry Button */
@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 107, 255, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 107, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 107, 255, 0);
    }
}

.pulse-button {
    animation: pulse 2s infinite;
}

/* Floating Animation for Empty State */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.empty-state img {
    animation: float 3s ease-in-out infinite;
}

/* Add these styles for the logo images */

/* Logo styles */
.logo img {
    height: 40px;
    transition: transform 0.5s ease;
    margin-right: 12px;
}

.logo:hover img {
    transform: rotate(10deg) scale(1.1);
}

/* Button icon styles */
.button-icon {
    height: 20px;
    width: 20px;
    margin-right: 8px;
    vertical-align: middle;
    display: inline-block;
}

/* Adjust button with icon */
.button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Login page specific logo */
.auth-container .logo img {
    height: 80px;
    margin-bottom: 20px;
}

/* Ensure the images are properly sized in different contexts */
@media (max-width: 768px) {
    .logo img {
        height: 32px;
    }

    .auth-container .logo img {
        height: 60px;
    }
}

/* Enhanced Logo Animations */

/* Main Journal Logo Animation */
@keyframes notebook-animation {
  0% { transform: rotate(0) scale(1); }
  25% { transform: rotate(3deg) scale(1.05); }
  50% { transform: rotate(0) scale(1); }
  75% { transform: rotate(-3deg) scale(1.05); }
  100% { transform: rotate(0) scale(1); }
}

.notebook-logo {
  animation: notebook-animation 6s ease-in-out infinite;
  transform-origin: center;
}

.logo:hover .notebook-logo {
  animation-duration: 1.5s;
}

/* Login/Signup News Logo Animation */
@keyframes news-logo-animation {
  0% { transform: translateY(0) rotate(0); }
  25% { transform: translateY(-5px) rotate(2deg); }
  50% { transform: translateY(0) rotate(0); }
  75% { transform: translateY(-3px) rotate(-2deg); }
  100% { transform: translateY(0) rotate(0); }
}

.auth-container .logo img {
  animation: news-logo-animation 4s ease-in-out infinite;
}

.auth-container .logo:hover img {
  animation-duration: 1s;
}

/* New Entry Book Logo Animation */
@keyframes book-open-animation {
  0% { transform: scale(1) translateY(0); }
  50% { transform: scale(1.15) translateY(-3px); }
  100% { transform: scale(1) translateY(0); }
}

.button-icon.book-icon {
  animation: book-open-animation 3s ease-in-out infinite;
  transform-origin: center;
}

.button:hover .button-icon.book-icon {
  animation-duration: 0.8s;
}

/* Animated Icon for Empty State */
@keyframes empty-state-animation {
  0% { transform: translateY(0) scale(1); opacity: 0.8; }
  50% { transform: translateY(-15px) scale(1.1); opacity: 1; }
  100% { transform: translateY(0) scale(1); opacity: 0.8; }
}

.empty-state img {
  animation: empty-state-animation 3s ease-in-out infinite;
}

/* Create a pulsing effect for the new entry button */
@keyframes pulse-glow {
  0% { box-shadow: 0 0 0 0 rgba(74, 107, 255, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(74, 107, 255, 0); }
  100% { box-shadow: 0 0 0 0 rgba(74, 107, 255, 0); }
}

.pulse-button {
  animation: pulse-glow 2s infinite;
}

/* SVG Logo Container Styles */
.svg-logo-container {
  display: inline-block;
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
}

.logo svg {
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
  transition: all 0.3s ease;
}

.logo:hover svg {
  filter: drop-shadow(0 6px 12px rgba(0, 0, 0, 0.5));
  transform: translateY(-3px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .svg-logo-container {
    width: 60px;
    height: 60px;
  }
}

/* Add glow effect to focused inputs */
input:focus, textarea:focus, select:focus {
    outline: none;
    border-color: var(--accent-purple);
    box-shadow: 0 0 0 3px rgba(191, 0, 255, 0.2);
}

/* Theme switcher styles */
.theme-switcher {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
    background: var(--card-bg);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow);
    cursor: pointer;
    border: 1px solid var(--border-color);
    transition: var(--transition);
    display: none; /* Hide the fixed version */
}

.theme-switcher:hover {
    transform: scale(1.1);
}

.theme-switcher i {
    font-size: 1.5rem;
    color: var(--text-color);
}

/* Style the theme switcher in the nav */
nav a#themeSwitcher {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

nav a#themeSwitcher:hover .animated-icon {
    transform: rotate(20deg);
}

/* Add a subtle indicator for the current theme */
nav a#themeSwitcher::after {
    content: '';
    display: block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--accent-teal);
    margin-left: 5px;
}

[data-theme="light"] nav a#themeSwitcher::after {
    background: var(--accent-purple);
}

/* Adjust placeholder color based on theme */
::placeholder {
    color: var(--text-color);
    opacity: 0.7;
}

/* Voice input styles */
.voice-input-container {
    position: relative;
    display: flex;
    flex-direction: column;
}

.voice-btn {
    position: absolute;
    right: 10px;
    top: 10px;
    background: var(--accent-purple);
    color: white;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    z-index: 2;
}

.voice-btn:hover {
    transform: scale(1.1);
    background: var(--accent-pink);
}

.voice-btn.recording {
    background: #e74c3c;
    animation: pulse 1.5s infinite;
}

.voice-status {
    margin-top: 8px;
    font-size: 0.85rem;
    color: var(--text-color);
    opacity: 0.7;
    transition: all 0.3s ease;
}

.voice-status.recording {
    color: #e74c3c;
    opacity: 1;
    font-weight: 500;
}

.voice-status.error {
    color: #e74c3c;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(231, 76, 60, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(231, 76, 60, 0);
    }
}

/* Adjust textarea to accommodate the voice button */
#id_content {
    padding-right: 60px;
    min-height: 150px;
    resize: vertical;
}

/* Light theme adjustments for voice input */
[data-theme="light"] .voice-btn {
    background: var(--accent-purple);
    color: white;
}

[data-theme="light"] .voice-btn:hover {
    background: var(--accent-pink);
}

[data-theme="light"] .voice-btn.recording {
    background: #e74c3c;
}

[data-theme="light"] .voice-status {
    color: #555;
}

[data-theme="light"] .voice-status.recording {
    color: #e74c3c;
}

/* Enhanced form actions with better button styling */
.form-actions {
    display: flex;
    gap: 1.5rem;
    margin-top: 2.5rem;
    justify-content: flex-start;
    align-items: center;
}

.form-actions button,
.form-actions .button {
    min-width: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.7rem;
    padding: 1rem 1.8rem;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1.05rem;
    letter-spacing: 0.5px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    overflow: hidden;
    z-index: 1;
}

/* Primary (Save) button with gradient and glow effect */
.form-actions .primary-btn {
    background: linear-gradient(135deg, var(--accent-purple), var(--primary), var(--accent-blue));
    background-size: 200% 200%;
    animation: gradientShift 5s ease infinite;
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(83, 70, 224, 0.4);
}

.form-actions .primary-btn:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(83, 70, 224, 0.6);
}

.form-actions .primary-btn:active {
    transform: translateY(-2px);
}

.form-actions .primary-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.7s ease;
    z-index: -1;
}

.form-actions .primary-btn:hover::before {
    left: 100%;
}

/* Secondary (Cancel) button with distinct styling */
.form-actions .secondary {
    background: transparent;
    color: var(--text-color);
    border: 2px solid rgba(255, 255, 255, 0.15);
    position: relative;
    z-index: 1;
    overflow: hidden;
}

.form-actions .secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    z-index: -1;
    transform: scaleX(0);
    transform-origin: left;
    transition: transform 0.3s ease;
}

.form-actions .secondary:hover::before {
    transform: scaleX(1);
}

.form-actions .secondary:hover {
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* Light theme button adjustments */
[data-theme="light"] .form-actions .primary-btn {
    box-shadow: 0 4px 15px rgba(83, 70, 224, 0.25);
}

[data-theme="light"] .form-actions .primary-btn:hover {
    box-shadow: 0 8px 25px rgba(83, 70, 224, 0.4);
}

[data-theme="light"] .form-actions .secondary {
    background: transparent;
    color: #333;
    border: 2px solid rgba(0, 0, 0, 0.15);
}

[data-theme="light"] .form-actions .secondary::before {
    background: rgba(0, 0, 0, 0.05);
}

[data-theme="light"] .form-actions .secondary:hover {
    border-color: rgba(0, 0, 0, 0.3);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Danger button for confirmation dialog */
.button.danger {
    background: linear-gradient(135deg, #ff416c, #ff4b2b);
    color: white;
    border: none;
    box-shadow: 0 4px 15px rgba(255, 65, 108, 0.4);
}

.button.danger:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(255, 65, 108, 0.6);
}

/* Enhanced icon animations */
.form-actions .animated-icon {
    transition: all 0.3s ease;
}

.form-actions button:hover .animated-icon,
.form-actions .button:hover .animated-icon {
    transform: translateY(-3px) rotate(5deg);
}

/* Button focus states */
.form-actions button:focus,
.form-actions .button:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(83, 70, 224, 0.3);
}

[data-theme="light"] .form-actions button:focus,
[data-theme="light"] .form-actions .button:focus {
    box-shadow: 0 0 0 3px rgba(83, 70, 224, 0.2);
}

/* Confirmation dialog enhanced styling */
.confirm-dialog-content {
    background: var(--card-bg);
    padding: 2.5rem;
    border-radius: 15px;
    max-width: 450px;
    width: 90%;
    text-align: center;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.4);
    transform: translateY(20px);
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    backdrop-filter: blur(15px);
    border: 1px solid var(--border-color);
    border-top: 4px solid var(--accent-orange);
}

.confirm-dialog.active .confirm-dialog-content {
    transform: translateY(0);
}

.confirm-dialog h3 {
    margin-bottom: 1.2rem;
    color: var(--text-color);
    font-size: 1.5rem;
    font-weight: 600;
}

.confirm-dialog p {
    margin-bottom: 2rem;
    color: var(--text-color);
    opacity: 0.9;
    font-size: 1.1rem;
    line-height: 1.6;
}

.confirm-dialog-actions {
    display: flex;
    gap: 1.2rem;
    justify-content: center;
}

.confirm-dialog-actions button,
.confirm-dialog-actions .button {
    min-width: 130px;
    padding: 0.9rem 1.5rem;
}

/* Reminders & Templates Styling */

/* Reminders Container */
.reminders-container {
    max-width: 900px;
    margin: 0 auto;
    padding: 2rem;
}

/* Streak Display */
.streak-display {
    display: flex;
    gap: 1.5rem;
    margin-top: 1.5rem;
    margin-bottom: 2rem;
}

.streak-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    flex: 1;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.streak-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.streak-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: linear-gradient(135deg, #ff6b47, #4a1a4a);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: var(--light);
}

.streak-card:nth-child(2) .streak-icon {
    background: linear-gradient(135deg, #6b5b4a, #4a1a4a);
}

.streak-info h3 {
    font-size: 1.8rem;
    margin: 0;
    font-weight: 700;
}

.streak-info p {
    margin: 0;
    opacity: 0.7;
    font-size: 0.9rem;
}

/* Reminders List */
.reminders-actions {
    margin-bottom: 2rem;
    display: flex;
    justify-content: flex-end;
}

.reminders-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.reminder-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.reminder-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reminder-card.inactive {
    opacity: 0.6;
}

.reminder-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.reminder-header h3 {
    margin: 0;
    font-size: 1.3rem;
}

/* Toggle Switch */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--accent-orange);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--accent-orange);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.reminder-details {
    margin-bottom: 1.5rem;
}

.reminder-time, .reminder-frequency {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.reminder-actions {
    display: flex;
    gap: 0.5rem;
}

/* Templates Styling */
.templates-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.templates-grid {
    margin-top: 2rem;
}

.section-title {
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
    position: relative;
    padding-left: 1rem;
    border-left: 4px solid var(--accent-orange);
}

.template-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.template-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    position: relative;
    overflow: hidden;
}

.template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.template-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, var(--accent-orange), var(--primary));
}

.template-card[data-type="gratitude"]::before {
    background: linear-gradient(90deg, #ff6b47, #4a1a4a);
}

.template-card[data-type="reflection"]::before {
    background: linear-gradient(90deg, #4a1a4a, #6b5b4a);
}

.template-card[data-type="dream"]::before {
    background: linear-gradient(90deg, #6b5b4a, #f5f5dc);
}

.template-card[data-type="goal"]::before {
    background: linear-gradient(90deg, #ff6b47, #f5f5dc);
}

.template-card[data-type="travel"]::before {
    background: linear-gradient(90deg, #f5f5dc, #ff6b47);
}

.template-card[data-type="food"]::before {
    background: linear-gradient(90deg, #ff6b47, #6b5b4a);
}

.template-card[data-type="custom"]::before {
    background: linear-gradient(90deg, #6b5b4a, #4a1a4a);
}

.template-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--accent-orange), var(--primary));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    color: var(--light);
    margin-bottom: 1rem;
    transition: transform 0.3s ease;
}

.template-card:hover .template-icon {
    transform: scale(1.1) rotate(5deg);
}

.template-info {
    flex-grow: 1;
}

.template-info h3 {
    margin-top: 0;
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.template-info p {
    margin-top: 0;
    opacity: 0.8;
    font-size: 0.95rem;
    line-height: 1.5;
}

.template-actions {
    margin-top: 1.5rem;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.template-type-badge {
    display: inline-block;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    font-size: 0.8rem;
    background: var(--accent-orange);
    color: var(--light);
    margin-top: 0.8rem;
}

.template-type-badge.custom {
    background: var(--dark-accent);
}

/* Preview button styling */
.preview-btn {
    background-color: var(--dark-surface) !important;
    color: var(--text-color) !important;
}

[data-theme="light"] .preview-btn {
    background-color: #f0f0f0 !important;
}

.preview-btn:hover {
    background-color: var(--dark-accent) !important;
}

[data-theme="light"] .preview-btn:hover {
    background-color: #e0e0e0 !important;
}

/* Days selection styling */
.days-checkboxes {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.days-checkboxes label {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.5rem 1rem;
    background: var(--dark-surface);
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
}

[data-theme="light"] .days-checkboxes label {
    background: #f0f0f0;
}

.days-checkboxes label:hover {
    background: var(--dark-accent);
}

[data-theme="light"] .days-checkboxes label:hover {
    background: #e0e0e0;
}

.days-checkboxes input[type="checkbox"] {
    margin: 0;
}

/* Empty state for reminders */
.empty-state {
    text-align: center;
    padding: 3rem;
    background: var(--card-bg);
    border-radius: 12px;
    border: 1px solid var(--border-color);
    grid-column: 1 / -1;
}

.empty-state.small {
    padding: 2rem;
}

.empty-icon {
    font-size: 3rem;
    color: var(--accent-orange);
    opacity: 0.7;
    margin-bottom: 1rem;
}

.empty-state p {
    margin: 0.5rem 0;
    opacity: 0.8;
}

.empty-state .button {
    margin-top: 1.5rem;
}

/* Confirm delete styling */
.confirm-delete-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 60vh;
}

.confirm-delete-card {
    background: var(--card-bg);
    border-radius: 12px;
    padding: 2.5rem;
    text-align: center;
    max-width: 500px;
    width: 90%;
    border: 1px solid var(--border-color);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.confirm-icon {
    font-size: 3rem;
    color: var(--accent-orange);
    margin-bottom: 1.5rem;
}

.confirm-delete-card h2 {
    margin-top: 0;
    margin-bottom: 1rem;
}

.confirm-delete-card p {
    margin-bottom: 0.5rem;
    opacity: 0.8;
}

.confirm-delete-card .form-actions {
    margin-top: 2rem;
    justify-content: center;
}

/* Profile styles */
.profile-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.profile-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.stat-card {
    flex: 1;
    text-align: center;
    padding: 20px;
    margin: 0 10px;
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2);
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2rem;
    color: #6a11cb;
    margin-bottom: 10px;
}

.stat-card h3 {
    font-size: 2rem;
    margin: 5px 0;
    color: #333;
}

.profile-forms {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.form-section {
    flex: 1;
    min-width: 300px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.remember-me input {
    margin-right: 10px;
}

.auth-links {
    margin-top: 20px;
}

.auth-links p {
    margin: 5px 0;
}

/* Enhanced theme switcher styles */
.theme-nav-link {
    display: flex !important;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 0.5rem 1rem !important;
    transition: all 0.3s ease;
}

.theme-nav-link:hover {
    background: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

.theme-nav-link .animated-icon {
    transition: transform 0.5s ease;
}

.theme-nav-link:hover .animated-icon {
    transform: rotate(30deg) scale(1.2);
}

/* Add a subtle indicator for the current theme */
[data-theme="dark"] .theme-nav-link {
    border: 1px solid rgba(255, 255, 255, 0.2);
}

[data-theme="light"] .theme-nav-link {
    border: 1px solid rgba(255, 255, 255, 0.3);
}

/* Add a subtle glow effect to the theme switcher */
[data-theme="dark"] .theme-nav-link:hover {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.2);
}

[data-theme="light"] .theme-nav-link:hover {
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* Theme switcher text contrast improvements */
[data-theme="light"] .theme-nav-link {
    color: white;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}






